from typing import List, Optional
from sqlmodel import Session, select
from sqlalchemy.ext.asyncio import AsyncSession
from core.services.database.schemas.user_uploaded_files import (
    UserUploadedFilesTable,
    UserUploadedFilesCreate,
    UserUploadedFilesUpdate,
    FileStatus
)


class UserUploadedFilesCRUD:
    """用户上传文件的CRUD操作类"""

    async def create(
        self, 
        db: AsyncSession, 
        *, 
        obj_in: UserUploadedFilesCreate
    ) -> UserUploadedFilesTable:
        """创建新的文件上传记录"""
        db_obj = UserUploadedFilesTable.model_validate(obj_in)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get(
        self, 
        db: AsyncSession, 
        id: str
    ) -> Optional[UserUploadedFilesTable]:
        """根据ID获取文件记录"""
        statement = select(UserUploadedFilesTable).where(UserUploadedFilesTable.id == id)
        result = await db.exec(statement)
        return result.first()

    async def get_by_user_id(
        self, 
        db: AsyncSession, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[UserUploadedFilesTable]:
        """根据用户ID获取文件列表"""
        statement = (
            select(UserUploadedFilesTable)
            .where(UserUploadedFilesTable.user_id == user_id)
            .order_by(UserUploadedFilesTable.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.exec(statement)
        return result.all()

    async def get_by_file_hash(
        self, 
        db: AsyncSession, 
        file_hash: str, 
        user_id: Optional[int] = None
    ) -> Optional[UserUploadedFilesTable]:
        """根据文件哈希值获取文件记录（可选择性过滤用户）"""
        statement = select(UserUploadedFilesTable).where(
            UserUploadedFilesTable.file_hash == file_hash
        )
        if user_id is not None:
            statement = statement.where(UserUploadedFilesTable.user_id == user_id)
        
        result = await db.exec(statement)
        return result.first()

    async def get_by_ragflow_doc_id(
        self, 
        db: AsyncSession, 
        ragflow_doc_id: str
    ) -> Optional[UserUploadedFilesTable]:
        """根据RAGFlow文档ID获取文件记录"""
        statement = select(UserUploadedFilesTable).where(
            UserUploadedFilesTable.ragflow_doc_id == ragflow_doc_id
        )
        result = await db.exec(statement)
        return result.first()

    async def get_by_status(
        self, 
        db: AsyncSession, 
        status: FileStatus, 
        user_id: Optional[int] = None,
        skip: int = 0, 
        limit: int = 100
    ) -> List[UserUploadedFilesTable]:
        """根据状态获取文件列表"""
        statement = select(UserUploadedFilesTable).where(
            UserUploadedFilesTable.status == status
        )
        if user_id is not None:
            statement = statement.where(UserUploadedFilesTable.user_id == user_id)
        
        statement = statement.order_by(UserUploadedFilesTable.created_at.desc()).offset(skip).limit(limit)
        result = await db.exec(statement)
        return result.all()

    async def update(
        self, 
        db: AsyncSession, 
        *, 
        db_obj: UserUploadedFilesTable, 
        obj_in: UserUploadedFilesUpdate
    ) -> UserUploadedFilesTable:
        """更新文件记录"""
        obj_data = obj_in.model_dump(exclude_unset=True)
        for field, value in obj_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update_status(
        self, 
        db: AsyncSession, 
        *, 
        file_id: str, 
        status: FileStatus, 
        error_message: Optional[str] = None
    ) -> Optional[UserUploadedFilesTable]:
        """更新文件状态"""
        db_obj = await self.get(db, file_id)
        if db_obj:
            update_data = UserUploadedFilesUpdate(
                status=status,
                error_message=error_message
            )
            return await self.update(db, db_obj=db_obj, obj_in=update_data)
        return None

    async def delete(
        self, 
        db: AsyncSession, 
        *, 
        id: str
    ) -> Optional[UserUploadedFilesTable]:
        """删除文件记录"""
        db_obj = await self.get(db, id)
        if db_obj:
            await db.delete(db_obj)
            await db.commit()
        return db_obj

    async def count_by_user(
        self, 
        db: AsyncSession, 
        user_id: int
    ) -> int:
        """统计用户上传的文件数量"""
        statement = select(UserUploadedFilesTable).where(
            UserUploadedFilesTable.user_id == user_id
        )
        result = await db.exec(statement)
        return len(result.all())


# 创建CRUD实例
user_uploaded_files_crud = UserUploadedFilesCRUD()
